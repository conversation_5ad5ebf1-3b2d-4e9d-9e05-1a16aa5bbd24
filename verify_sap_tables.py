import os
from databricks import sql
import pandas as pd
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv('Germany_Scripts/.env')

def get_databricks_connection():
    """Establish connection to Databricks SQL warehouse"""
    print("\nChecking environment variables:")
    print(f"DATABRICKS_HOST: {os.getenv('DATABRICKS_HOST')}")
    print(f"DATABRICKS_HTTP_PATH: {os.getenv('DATABRICKS_HTTP_PATH')}")
    print(f"DATABRICKS_TOKEN: {'*****' if os.getenv('DATABRICKS_TOKEN') else None}")
    
    connection = sql.connect(
        server_hostname=os.getenv("DATABRICKS_HOST"),
        http_path=os.getenv("DATABRICKS_HTTP_PATH"),
        access_token=os.getenv("DATABRICKS_TOKEN")
    )
    return connection

def verify_table_access(table_name, required_columns, german_plants, plant_col=None):
    """Verify access to a specific SAP table with required columns"""
    connection = None
    try:
        connection = get_databricks_connection()
        with connection.cursor() as cursor:
            # Check table exists and has required columns
            columns_str = ','.join(required_columns)
            if plant_col and german_plants:
                query = f"SELECT {columns_str} FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.{table_name} WHERE {plant_col} IN {tuple(german_plants)} LIMIT 1"
            else:
                query = f"SELECT {columns_str} FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.{table_name} LIMIT 1"
            
            print(f"Executing query: {query.split('LIMIT')[0]}...")
            cursor.execute(query)
            result = cursor.fetchall_arrow()
            print(f"✅ Successfully accessed {table_name} with required columns")
            return True
            
    except Exception as e:
        print(f"❌ Error accessing {table_name}: {str(e)}")
        return False
    finally:
        if connection:
            connection.close()

def main():
    german_plants = ('DE02', 'DE05', 'DE06', 'DE08', 'DE30')
    
    # Define required columns and plant filter columns for each table
    tables_to_verify = {
        'mseg': {
            'columns': ['bwart', 'matnr', 'werks', 'shkzg', 'menge', 'cpudt_mkpf', 'lgort'],
            'plant_col': 'werks'
        },
        'mbew': {
            'columns': ['matnr', 'bwkey', 'verpr', 'peinh', 'lbkum', 'salk3'],
            'plant_col': 'bwkey'
        },
        'makt': {
            'columns': ['matnr', 'maktx', 'spras'],
            'plant_col': None  # makt doesn't have plant info
        }
    }
    
    print("=== SAP Table Accessibility Verification ===")
    print(f"Checking tables for German plants: {', '.join(german_plants)}\n")
    
    all_tables_accessible = True
    for table, columns in tables_to_verify.items():
        if not verify_table_access(table, columns['columns'], german_plants, columns['plant_col']):
            all_tables_accessible = False
    
    print("\nVerification complete!")
    if all_tables_accessible:
        print("✅ All required tables are accessible with the expected columns")
    else:
        print("❌ Some tables could not be accessed or are missing required columns")

if __name__ == "__main__":
    main()