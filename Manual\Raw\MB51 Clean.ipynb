{"cells": [{"cell_type": "code", "execution_count": 1, "id": "75aa71f9-80c8-40dc-9644-3fe9897b1096", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import re\n", "import os\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "7d8f4ef3", "metadata": {}, "outputs": [], "source": ["folder = r'C:\\Users\\<USER>\\OneDrive - Anheuser-Busch InBev\\Documents\\Germany Impairment\\W31\\MB51'\n", "iteration = \"Aug-W1\"\n", "file = \"MB51 Export.XLSX\"\n", "# ofile = \"MB51_P2.xlsx\""]}, {"cell_type": "markdown", "id": "d85d3aa6-42ec-44ee-b38b-d35bac819776", "metadata": {}, "source": ["# Clean"]}, {"cell_type": "code", "execution_count": 3, "id": "c4bd3d60-a06c-4fba-9b3e-c668b6916ae1", "metadata": {}, "outputs": [], "source": ["\n", "# df = pd.read_excel(os.path.join(folder,iteration,\"Raw\",file))"]}, {"cell_type": "code", "execution_count": 4, "id": "762e02e1", "metadata": {}, "outputs": [], "source": ["# Path to the workbook\n", "path = os.path.join(file)\n", "\n", "# 1. Load *all* sheets into a dict of DataFrames\n", "sheets = pd.read_excel(path, sheet_name=None)   # sheet_name=None ⇒ every sheet\n", "\n", "# 2. Combine them into one big DataFrame\n", "df = (\n", "    pd.concat(sheets.values(), ignore_index=True)  # append rows from all sheets\n", "      # .pipe(lambda d: d.assign(sheet_name=np.repeat(list(sheets), [len(s) for s in sheets.values()])))\n", "      # optional: keep the sheet name as a column for traceability\n", ")\n", "\n", "# df now holds the data from every sheet"]}, {"cell_type": "code", "execution_count": 5, "id": "3ca1809c-c603-416c-bbe2-763c385501f2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>---------------------------------------------------------------------------------------------------------------------------------------------------</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>|Material Material Description                ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>|MvT SLoc Mat. Doc.     Qty in UnE EUn Pstng D...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>----------------------------------------------...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>|7500229  GLASS LEFF 25CL                     ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>|101 1050 5005950087        3.240  PC  05.04.2...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  ---------------------------------------------------------------------------------------------------------------------------------------------------\n", "0  |Material Material Description                ...                                                                                                 \n", "1  |MvT SLoc Mat. Doc.     Qty in UnE EUn Pstng D...                                                                                                 \n", "2  ----------------------------------------------...                                                                                                 \n", "3  |7500229  GLASS LEFF 25CL                     ...                                                                                                 \n", "4  |101 1050 5005950087        3.240  PC  05.04.2...                                                                                                 "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "0f80281a-938c-426b-9ab0-316d9703384c", "metadata": {}, "outputs": [], "source": ["df.columns = [\"Test\"]"]}, {"cell_type": "code", "execution_count": 7, "id": "3c7f2773", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Test</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>|Material Material Description                ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>|MvT SLoc Mat. Doc.     Qty in UnE EUn Pstng D...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>----------------------------------------------...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>|7500229  GLASS LEFF 25CL                     ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>|101 1050 5005950087        3.240  PC  05.04.2...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                Test\n", "0  |Material Material Description                ...\n", "1  |MvT SLoc Mat. Doc.     Qty in UnE EUn Pstng D...\n", "2  ----------------------------------------------...\n", "3  |7500229  GLASS LEFF 25CL                     ...\n", "4  |101 1050 5005950087        3.240  PC  05.04.2..."]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 8, "id": "31bc18d2-a5c7-4f7b-ac69-9ab87c119d4b", "metadata": {"scrolled": true}, "outputs": [], "source": ["split_dfs = []\n", "temp = []\n", "for value in df['Test']:\n", "    # Checks if the value is a sequence of dashes to split blocks\n", "    if re.match(r'-+', value):\n", "        if temp:\n", "            split_dfs.append(pd.DataFrame({'Col': temp}))\n", "            temp = []\n", "    else:\n", "        temp.append(value)\n", "\n", "# Append the last chunk of data if it exists\n", "if temp:\n", "    split_dfs.append(pd.DataFrame({'Col': temp}))\n", "    \n", "df_names = []\n", "filtered_dfs = []\n", "\n", "# --- Filter out \"Total\" blocks before further processing ---\n", "for block_df in split_dfs:\n", "    if not block_df.empty:\n", "        first_line = block_df['Col'].iloc[0]\n", "        # If the first line is not a \"Total\" summary, keep the block\n", "        if '* Total' not in first_line:\n", "            filtered_dfs.append(block_df)\n", "\n", "# Now create the numbered DataFrames (e.g., df1, df2) from the clean, filtered list\n", "for i, split_df in enumerate(filtered_dfs, 1):\n", "    df_name = f\"df{i}\"\n", "    globals()[f\"df{i}\"] = split_df\n", "    df_names.append(df_name)"]}, {"cell_type": "code", "execution_count": 9, "id": "1cd0bc47-aa2d-4803-b435-3494a50b49d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2441\n"]}], "source": ["print(len(df_names))"]}, {"cell_type": "markdown", "id": "310d3461-d328-422e-8bd4-c83bc5431134", "metadata": {}, "source": ["## First Row "]}, {"cell_type": "code", "execution_count": 10, "id": "a2f27624-9163-49b3-a88e-39fd88c16dd9", "metadata": {}, "outputs": [], "source": ["# Function to extract the required information and return a row for the existing DataFrame\n", "def extract_data_and_append(row):\n", "    \n", "    text = row.strip('|').strip()\n", "    \n", "    # Create a default empty result. This will be returned for any row that can't be parsed.\n", "    result = {f'Column {i}': '' for i in range(1, 19)}\n", "\n", "    try:\n", "        # This block will attempt to parse the row as before.\n", "        # If any part of it fails (due to an unexpected format), the IndexError will be caught.\n", "        \n", "        # Extract the parts using regular expressions\n", "        number = re.findall(r'\\d+', text.split()[0])[0]\n", "        before_de = re.findall(r'(?<=\\d\\s)(.*?)(?=DE)', text)[0].strip()\n", "        de_code = re.findall(r'DE\\d+', text)[0]\n", "        after_de = text.split(de_code)[-1].strip()\n", "\n", "        # If parsing is successful, populate the result dictionary\n", "        result = {\n", "            'Column 1': '',\n", "            'Column 2': number,  \n", "            'Column 3': '',  \n", "            'Column 4': before_de,  \n", "            'Column 5': '',  \n", "            'Column 6': '', \n", "            'Column 7': '',\n", "            'Column 8': '',\n", "            'Column 9': de_code,\n", "            'Column 10': after_de,\n", "            'Column 11': '',\n", "            'Column 12': '', \n", "            'Column 13': '',  \n", "            'Column 14': '',\n", "            'Column 15': '',\n", "            'Column 16': '',\n", "            'Column 17': '',\n", "            'Column 18': ''  \n", "        }\n", "    except IndexError:\n", "        # If an IndexError occurs, it means the row format was invalid.\n", "        # We do nothing here, and the default empty 'result' dictionary is returned.\n", "        pass\n", "    \n", "    return result"]}, {"cell_type": "markdown", "id": "d94c4a31-69b4-4e6b-82ba-93d519865ff4", "metadata": {}, "source": ["## All rows except first"]}, {"cell_type": "code", "execution_count": 11, "id": "33ac8b42-28c4-49a3-8992-9c8b69d2127f", "metadata": {}, "outputs": [], "source": ["def process_row(row):\n", "    # Strip the leading and trailing pipes and extra spaces\n", "    row = row.strip('|').strip()\n", "    \n", "    # Split the row into parts based on spaces\n", "    parts = re.split(r'\\s{1,}', row)  # Split by 2 or more spaces\n", "\n", "    #print(parts)\n", "    \n", "    # Extract the values from the parts\n", "    col_2 = parts[0]\n", "    col_3 = parts[1]\n", "    col_4 = parts[2]\n", "    col_5 = parts[3]\n", "    col_6 = parts[4]  # Text after the first 4 numbers\n", "    col_11 = parts[5]  # First date\n", "    col_13 = parts[6]  # Second date\n", "    #col_13 = parts[7]  # Time\n", "    col_18 = parts[-1]  # Last number\n", "    \n", "    # Create a dictionary to hold the column mappings\n", "    result = {\n", "        'Column 1': '',\n", "        'Column 2': col_2,  # First number\n", "        'Column 3': col_3,  # Second number\n", "        'Column 4': col_4,  # Third number\n", "        'Column 5': col_5,  # Fourth number\n", "        'Column 6': col_6,  # Text\n", "        'Column 7': '',\n", "        'Column 8': '',\n", "        'Column 9': '',\n", "        'Column 10': '',\n", "        'Column 11': col_11,  # First date\n", "        'Column 12': '',  # Second date\n", "        'Column 13': col_13,  # Time\n", "        'Column 14': '',\n", "        'Column 15': '',\n", "        'Column 16': '',\n", "        'Column 17': '',\n", "        'Column 18': col_18  # Last number\n", "    }\n", "    \n", "    return result"]}, {"cell_type": "markdown", "id": "cd5c6e22-a1d2-48b7-823f-01cc36d901c3", "metadata": {}, "source": ["## Combined "]}, {"cell_type": "code", "execution_count": 12, "id": "26d0fc2c-978e-4b66-a689-4acc87b27e45", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running for df2\n", "Running for df3\n", "Running for df4\n", "Running for df5\n", "Running for df6\n", "Running for df7\n", "Running for df8\n", "Running for df9\n", "Running for df10\n", "Running for df11\n", "Running for df12\n", "Running for df13\n", "Running for df14\n", "Running for df15\n", "Running for df16\n", "Running for df17\n", "Running for df18\n", "Running for df19\n", "Running for df20\n", "Running for df21\n", "Running for df22\n", "Running for df23\n", "Running for df24\n", "Running for df25\n", "Running for df26\n", "Running for df27\n", "Running for df28\n", "Running for df29\n", "Running for df30\n", "Running for df31\n", "Running for df32\n", "Running for df33\n", "Running for df34\n", "Running for df35\n", "Running for df36\n", "Running for df37\n", "Running for df38\n", "Running for df39\n", "Running for df40\n", "Running for df41\n", "Running for df42\n", "Running for df43\n", "Running for df44\n", "Running for df45\n", "Running for df46\n", "Running for df47\n", "Running for df48\n", "Running for df49\n", "Running for df50\n", "Running for df51\n", "Running for df52\n", "Running for df53\n", "Running for df54\n", "Running for df55\n", "Running for df56\n", "Running for df57\n", "Running for df58\n", "Running for df59\n", "Running for df60\n", "Running for df61\n", "Running for df62\n", "Running for df63\n", "Running for df64\n", "Running for df65\n", "Running for df66\n", "Running for df67\n", "Running for df68\n", "Running for df69\n", "Running for df70\n", "Running for df71\n", "Running for df72\n", "Running for df73\n", "Running for df74\n", "Running for df75\n", "Running for df76\n", "Running for df77\n", "Running for df78\n", "Running for df79\n", "Running for df80\n", "Running for df81\n", "Running for df82\n", "Running for df83\n", "Running for df84\n", "Running for df85\n", "Running for df86\n", "Running for df87\n", "Running for df88\n", "Running for df89\n", "Running for df90\n", "Running for df91\n", "Running for df92\n", "Running for df93\n", "Running for df94\n", "Running for df95\n", "Running for df96\n", "Running for df97\n", "Running for df98\n", "Running for df99\n", "Running for df100\n", "Running for df101\n", "Running for df102\n", "Running for df103\n", "Running for df104\n", "Running for df105\n", "Running for df106\n", "Running for df107\n", "Running for df108\n", "Running for df109\n", "Running for df110\n", "Running for df111\n", "Running for df112\n", "Running for df113\n", "Running for df114\n", "Running for df115\n", "Running for df116\n", "Running for df117\n", "Running for df118\n", "Running for df119\n", "Running for df120\n", "Running for df121\n", "Running for df122\n", "Running for df123\n", "Running for df124\n", "Running for df125\n", "Running for df126\n", "Running for df127\n", "Running for df128\n", "Running for df129\n", "Running for df130\n", "Running for df131\n", "Running for df132\n", "Running for df133\n", "Running for df134\n", "Running for df135\n", "Running for df136\n", "Running for df137\n", "Running for df138\n", "Running for df139\n", "Running for df140\n", "Running for df141\n", "Running for df142\n", "Running for df143\n", "Running for df144\n", "Running for df145\n", "Running for df146\n", "Running for df147\n", "Running for df148\n", "Running for df149\n", "Running for df150\n", "Running for df151\n", "Running for df152\n", "Running for df153\n", "Running for df154\n", "Running for df155\n", "Running for df156\n", "Running for df157\n", "Running for df158\n", "Running for df159\n", "Running for df160\n", "Running for df161\n", "Running for df162\n", "Running for df163\n", "Running for df164\n", "Running for df165\n", "Running for df166\n", "Running for df167\n", "Running for df168\n", "Running for df169\n", "Running for df170\n", "Running for df171\n", "Running for df172\n", "Running for df173\n", "Running for df174\n", "Running for df175\n", "Running for df176\n", "Running for df177\n", "Running for df178\n", "Running for df179\n", "Running for df180\n", "Running for df181\n", "Running for df182\n", "Running for df183\n", "Running for df184\n", "Running for df185\n", "Running for df186\n", "Running for df187\n", "Running for df188\n", "Running for df189\n", "Running for df190\n", "Running for df191\n", "Running for df192\n", "Running for df193\n", "Running for df194\n", "Running for df195\n", "Running for df196\n", "Running for df197\n", "Running for df198\n", "Running for df199\n", "Running for df200\n", "Running for df201\n", "Running for df202\n", "Running for df203\n", "Running for df204\n", "Running for df205\n", "Running for df206\n", "Running for df207\n", "Running for df208\n", "Running for df209\n", "Running for df210\n", "Running for df211\n", "Running for df212\n", "Running for df213\n", "Running for df214\n", "Running for df215\n", "Running for df216\n", "Running for df217\n", "Running for df218\n", "Running for df219\n", "Running for df220\n", "Running for df221\n", "Running for df222\n", "Running for df223\n", "Running for df224\n", "Running for df225\n", "Running for df226\n", "Running for df227\n", "Running for df228\n", "Running for df229\n", "Running for df230\n", "Running for df231\n", "Running for df232\n", "Running for df233\n", "Running for df234\n", "Running for df235\n", "Running for df236\n", "Running for df237\n", "Running for df238\n", "Running for df239\n", "Running for df240\n", "Running for df241\n", "Running for df242\n", "Running for df243\n", "Running for df244\n", "Running for df245\n", "Running for df246\n", "Running for df247\n", "Running for df248\n", "Running for df249\n", "Running for df250\n", "Running for df251\n", "Running for df252\n", "Running for df253\n", "Running for df254\n", "Running for df255\n", "Running for df256\n", "Running for df257\n", "Running for df258\n", "Running for df259\n", "Running for df260\n", "Running for df261\n", "Running for df262\n", "Running for df263\n", "Running for df264\n", "Running for df265\n", "Running for df266\n", "Running for df267\n", "Running for df268\n", "Running for df269\n", "Running for df270\n", "Running for df271\n", "Running for df272\n", "Running for df273\n", "Running for df274\n", "Running for df275\n", "Running for df276\n", "Running for df277\n", "Running for df278\n", "Running for df279\n", "Running for df280\n", "Running for df281\n", "Running for df282\n", "Running for df283\n", "Running for df284\n", "Running for df285\n", "Running for df286\n", "Running for df287\n", "Running for df288\n", "Running for df289\n", "Running for df290\n", "Running for df291\n", "Running for df292\n", "Running for df293\n", "Running for df294\n", "Running for df295\n", "Running for df296\n", "Running for df297\n", "Running for df298\n", "Running for df299\n", "Running for df300\n", "Running for df301\n", "Running for df302\n", "Running for df303\n", "Running for df304\n", "Running for df305\n", "Running for df306\n", "Running for df307\n", "Running for df308\n", "Running for df309\n", "Running for df310\n", "Running for df311\n", "Running for df312\n", "Running for df313\n", "Running for df314\n", "Running for df315\n", "Running for df316\n", "Running for df317\n", "Running for df318\n", "Running for df319\n", "Running for df320\n", "Running for df321\n", "Running for df322\n", "Running for df323\n", "Running for df324\n", "Running for df325\n", "Running for df326\n", "Running for df327\n", "Running for df328\n", "Running for df329\n", "Running for df330\n", "Running for df331\n", "Running for df332\n", "Running for df333\n", "Running for df334\n", "Running for df335\n", "Running for df336\n", "Running for df337\n", "Running for df338\n", "Running for df339\n", "Running for df340\n", "Running for df341\n", "Running for df342\n", "Running for df343\n", "Running for df344\n", "Running for df345\n", "Running for df346\n", "Running for df347\n", "Running for df348\n", "Running for df349\n", "Running for df350\n", "Running for df351\n", "Running for df352\n", "Running for df353\n", "Running for df354\n", "Running for df355\n", "Running for df356\n", "Running for df357\n", "Running for df358\n", "Running for df359\n", "Running for df360\n", "Running for df361\n", "Running for df362\n", "Running for df363\n", "Running for df364\n", "Running for df365\n", "Running for df366\n", "Running for df367\n", "Running for df368\n", "Running for df369\n", "Running for df370\n", "Running for df371\n", "Running for df372\n", "Running for df373\n", "Running for df374\n", "Running for df375\n", "Running for df376\n", "Running for df377\n", "Running for df378\n", "Running for df379\n", "Running for df380\n", "Running for df381\n", "Running for df382\n", "Running for df383\n", "Running for df384\n", "Running for df385\n", "Running for df386\n", "Running for df387\n", "Running for df388\n", "Running for df389\n", "Running for df390\n", "Running for df391\n", "Running for df392\n", "Running for df393\n", "Running for df394\n", "Running for df395\n", "Running for df396\n", "Running for df397\n", "Running for df398\n", "Running for df399\n", "Running for df400\n", "Running for df401\n", "Running for df402\n", "Running for df403\n", "Running for df404\n", "Running for df405\n", "Running for df406\n", "Running for df407\n", "Running for df408\n", "Running for df409\n", "Running for df410\n", "Running for df411\n", "Running for df412\n", "Running for df413\n", "Running for df414\n", "Running for df415\n", "Running for df416\n", "Running for df417\n", "Running for df418\n", "Running for df419\n", "Running for df420\n", "Running for df421\n", "Running for df422\n", "Running for df423\n", "Running for df424\n", "Running for df425\n", "Running for df426\n", "Running for df427\n", "Running for df428\n", "Running for df429\n", "Running for df430\n", "Running for df431\n", "Running for df432\n", "Running for df433\n", "Running for df434\n", "Running for df435\n", "Running for df436\n", "Running for df437\n", "Running for df438\n", "Running for df439\n", "Running for df440\n", "Running for df441\n", "Running for df442\n", "Running for df443\n", "Running for df444\n", "Running for df445\n", "Running for df446\n", "Running for df447\n", "Running for df448\n", "Running for df449\n", "Running for df450\n", "Running for df451\n", "Running for df452\n", "Running for df453\n", "Running for df454\n", "Running for df455\n", "Running for df456\n", "Running for df457\n", "Running for df458\n", "Running for df459\n", "Running for df460\n", "Running for df461\n", "Running for df462\n", "Running for df463\n", "Running for df464\n", "Running for df465\n", "Running for df466\n", "Running for df467\n", "Running for df468\n", "Running for df469\n", "Running for df470\n", "Running for df471\n", "Running for df472\n", "Running for df473\n", "Running for df474\n", "Running for df475\n", "Running for df476\n", "Running for df477\n", "Running for df478\n", "Running for df479\n", "Running for df480\n", "Running for df481\n", "Running for df482\n", "Running for df483\n", "Running for df484\n", "Running for df485\n", "Running for df486\n", "Running for df487\n", "Running for df488\n", "Running for df489\n", "Running for df490\n", "Running for df491\n", "Running for df492\n", "Running for df493\n", "Running for df494\n", "Running for df495\n", "Running for df496\n", "Running for df497\n", "Running for df498\n", "Running for df499\n", "Running for df500\n", "Running for df501\n", "Running for df502\n", "Running for df503\n", "Running for df504\n", "Running for df505\n", "Running for df506\n", "Running for df507\n", "Running for df508\n", "Running for df509\n", "Running for df510\n", "Running for df511\n", "Running for df512\n", "Running for df513\n", "Running for df514\n", "Running for df515\n", "Running for df516\n", "Running for df517\n", "Running for df518\n", "Running for df519\n", "Running for df520\n", "Running for df521\n", "Running for df522\n", "Running for df523\n", "Running for df524\n", "Running for df525\n", "Running for df526\n", "Running for df527\n", "Running for df528\n", "Running for df529\n", "Running for df530\n", "Running for df531\n", "Running for df532\n", "Running for df533\n", "Running for df534\n", "Running for df535\n", "Running for df536\n", "Running for df537\n", "Running for df538\n", "Running for df539\n", "Running for df540\n", "Running for df541\n", "Running for df542\n", "Running for df543\n", "Running for df544\n", "Running for df545\n", "Running for df546\n", "Running for df547\n", "Running for df548\n", "Running for df549\n", "Running for df550\n", "Running for df551\n", "Running for df552\n", "Running for df553\n", "Running for df554\n", "Running for df555\n", "Running for df556\n", "Running for df557\n", "Running for df558\n", "Running for df559\n", "Running for df560\n", "Running for df561\n", "Running for df562\n", "Running for df563\n", "Running for df564\n", "Running for df565\n", "Running for df566\n", "Running for df567\n", "Running for df568\n", "Running for df569\n", "Running for df570\n", "Running for df571\n", "Running for df572\n", "Running for df573\n", "Running for df574\n", "Running for df575\n", "Running for df576\n", "Running for df577\n", "Running for df578\n", "Running for df579\n", "Running for df580\n", "Running for df581\n", "Running for df582\n", "Running for df583\n", "Running for df584\n", "Running for df585\n", "Running for df586\n", "Running for df587\n", "Running for df588\n", "Running for df589\n", "Running for df590\n", "Running for df591\n", "Running for df592\n", "Running for df593\n", "Running for df594\n", "Running for df595\n", "Running for df596\n", "Running for df597\n", "Running for df598\n", "Running for df599\n", "Running for df600\n", "Running for df601\n", "Running for df602\n", "Running for df603\n", "Running for df604\n", "Running for df605\n", "Running for df606\n", "Running for df607\n", "Running for df608\n", "Running for df609\n", "Running for df610\n", "Running for df611\n", "Running for df612\n", "Running for df613\n", "Running for df614\n", "Running for df615\n", "Running for df616\n", "Running for df617\n", "Running for df618\n", "Running for df619\n", "Running for df620\n", "Running for df621\n", "Running for df622\n", "Running for df623\n", "Running for df624\n", "Running for df625\n", "Running for df626\n", "Running for df627\n", "Running for df628\n", "Running for df629\n", "Running for df630\n", "Running for df631\n", "Running for df632\n", "Running for df633\n", "Running for df634\n", "Running for df635\n", "Running for df636\n", "Running for df637\n", "Running for df638\n", "Running for df639\n", "Running for df640\n", "Running for df641\n", "Running for df642\n", "Running for df643\n", "Running for df644\n", "Running for df645\n", "Running for df646\n", "Running for df647\n", "Running for df648\n", "Running for df649\n", "Running for df650\n", "Running for df651\n", "Running for df652\n", "Running for df653\n", "Running for df654\n", "Running for df655\n", "Running for df656\n", "Running for df657\n", "Running for df658\n", "Running for df659\n", "Running for df660\n", "Running for df661\n", "Running for df662\n", "Running for df663\n", "Running for df664\n", "Running for df665\n", "Running for df666\n", "Running for df667\n", "Running for df668\n", "Running for df669\n", "Running for df670\n", "Running for df671\n", "Running for df672\n", "Running for df673\n", "Running for df674\n", "Running for df675\n", "Running for df676\n", "Running for df677\n", "Running for df678\n", "Running for df679\n", "Running for df680\n", "Running for df681\n", "Running for df682\n", "Running for df683\n", "Running for df684\n", "Running for df685\n", "Running for df686\n", "Running for df687\n", "Running for df688\n", "Running for df689\n", "Running for df690\n", "Running for df691\n", "Running for df692\n", "Running for df693\n", "Running for df694\n", "Running for df695\n", "Running for df696\n", "Running for df697\n", "Running for df698\n", "Running for df699\n", "Running for df700\n", "Running for df701\n", "Running for df702\n", "Running for df703\n", "Running for df704\n", "Running for df705\n", "Running for df706\n", "Running for df707\n", "Running for df708\n", "Running for df709\n", "Running for df710\n", "Running for df711\n", "Running for df712\n", "Running for df713\n", "Running for df714\n", "Running for df715\n", "Running for df716\n", "Running for df717\n", "Running for df718\n", "Running for df719\n", "Running for df720\n", "Running for df721\n", "Running for df722\n", "Running for df723\n", "Running for df724\n", "Running for df725\n", "Running for df726\n", "Running for df727\n", "Running for df728\n", "Running for df729\n", "Running for df730\n", "Running for df731\n", "Running for df732\n", "Running for df733\n", "Running for df734\n", "Running for df735\n", "Running for df736\n", "Running for df737\n", "Running for df738\n", "Running for df739\n", "Running for df740\n", "Running for df741\n", "Running for df742\n", "Running for df743\n", "Running for df744\n", "Running for df745\n", "Running for df746\n", "Running for df747\n", "Running for df748\n", "Running for df749\n", "Running for df750\n", "Running for df751\n", "Running for df752\n", "Running for df753\n", "Running for df754\n", "Running for df755\n", "Running for df756\n", "Running for df757\n", "Running for df758\n", "Running for df759\n", "Running for df760\n", "Running for df761\n", "Running for df762\n", "Running for df763\n", "Running for df764\n", "Running for df765\n", "Running for df766\n", "Running for df767\n", "Running for df768\n", "Running for df769\n", "Running for df770\n", "Running for df771\n", "Running for df772\n", "Running for df773\n", "Running for df774\n", "Running for df775\n", "Running for df776\n", "Running for df777\n", "Running for df778\n", "Running for df779\n", "Running for df780\n", "Running for df781\n", "Running for df782\n", "Running for df783\n", "Running for df784\n", "Running for df785\n", "Running for df786\n", "Running for df787\n", "Running for df788\n", "Running for df789\n", "Running for df790\n", "Running for df791\n", "Running for df792\n", "Running for df793\n", "Running for df794\n", "Running for df795\n", "Running for df796\n", "Running for df797\n", "Running for df798\n", "Running for df799\n", "Running for df800\n", "Running for df801\n", "Running for df802\n", "Running for df803\n", "Running for df804\n", "Running for df805\n", "Running for df806\n", "Running for df807\n", "Running for df808\n", "Running for df809\n", "Running for df810\n", "Running for df811\n", "Running for df812\n", "Running for df813\n", "Running for df814\n", "Running for df815\n", "Running for df816\n", "Running for df817\n", "Running for df818\n", "Running for df819\n", "Running for df820\n", "Running for df821\n", "Running for df822\n", "Running for df823\n", "Running for df824\n", "Running for df825\n", "Running for df826\n", "Running for df827\n", "Running for df828\n", "Running for df829\n", "Running for df830\n", "Running for df831\n", "Running for df832\n", "Running for df833\n", "Running for df834\n", "Running for df835\n", "Running for df836\n", "Running for df837\n", "Running for df838\n", "Running for df839\n", "Running for df840\n", "Running for df841\n", "Running for df842\n", "Running for df843\n", "Running for df844\n", "Running for df845\n", "Running for df846\n", "Running for df847\n", "Running for df848\n", "Running for df849\n", "Running for df850\n", "Running for df851\n", "Running for df852\n", "Running for df853\n", "Running for df854\n", "Running for df855\n", "Running for df856\n", "Running for df857\n", "Running for df858\n", "Running for df859\n", "Running for df860\n", "Running for df861\n", "Running for df862\n", "Running for df863\n", "Running for df864\n", "Running for df865\n", "Running for df866\n", "Running for df867\n", "Running for df868\n", "Running for df869\n", "Running for df870\n", "Running for df871\n", "Running for df872\n", "Running for df873\n", "Running for df874\n", "Running for df875\n", "Running for df876\n", "Running for df877\n", "Running for df878\n", "Running for df879\n", "Running for df880\n", "Running for df881\n", "Running for df882\n", "Running for df883\n", "Running for df884\n", "Running for df885\n", "Running for df886\n", "Running for df887\n", "Running for df888\n", "Running for df889\n", "Running for df890\n", "Running for df891\n", "Running for df892\n", "Running for df893\n", "Running for df894\n", "Running for df895\n", "Running for df896\n", "Running for df897\n", "Running for df898\n", "Running for df899\n", "Running for df900\n", "Running for df901\n", "Running for df902\n", "Running for df903\n", "Running for df904\n", "Running for df905\n", "Running for df906\n", "Running for df907\n", "Running for df908\n", "Running for df909\n", "Running for df910\n", "Running for df911\n", "Running for df912\n", "Running for df913\n", "Running for df914\n", "Running for df915\n", "Running for df916\n", "Running for df917\n", "Running for df918\n", "Running for df919\n", "Running for df920\n", "Running for df921\n", "Running for df922\n", "Running for df923\n", "Running for df924\n", "Running for df925\n", "Running for df926\n", "Running for df927\n", "Running for df928\n", "Running for df929\n", "Running for df930\n", "Running for df931\n", "Running for df932\n", "Running for df933\n", "Running for df934\n", "Running for df935\n", "Running for df936\n", "Running for df937\n", "Running for df938\n", "Running for df939\n", "Running for df940\n", "Running for df941\n", "Running for df942\n", "Running for df943\n", "Running for df944\n", "Running for df945\n", "Running for df946\n", "Running for df947\n", "Running for df948\n", "Running for df949\n", "Running for df950\n", "Running for df951\n", "Running for df952\n", "Running for df953\n", "Running for df954\n", "Running for df955\n", "Running for df956\n", "Running for df957\n", "Running for df958\n", "Running for df959\n", "Running for df960\n", "Running for df961\n", "Running for df962\n", "Running for df963\n", "Running for df964\n", "Running for df965\n", "Running for df966\n", "Running for df967\n", "Running for df968\n", "Running for df969\n", "Running for df970\n", "Running for df971\n", "Running for df972\n", "Running for df973\n", "Running for df974\n", "Running for df975\n", "Running for df976\n", "Running for df977\n", "Running for df978\n", "Running for df979\n", "Running for df980\n", "Running for df981\n", "Running for df982\n", "Running for df983\n", "Running for df984\n", "Running for df985\n", "Running for df986\n", "Running for df987\n", "Running for df988\n", "Running for df989\n", "Running for df990\n", "Running for df991\n", "Running for df992\n", "Running for df993\n", "Running for df994\n", "Running for df995\n", "Running for df996\n", "Running for df997\n", "Running for df998\n", "Running for df999\n", "Running for df1000\n", "Running for df1001\n", "Running for df1002\n", "Running for df1003\n", "Running for df1004\n", "Running for df1005\n", "Running for df1006\n", "Running for df1007\n", "Running for df1008\n", "Running for df1009\n", "Running for df1010\n", "Running for df1011\n", "Running for df1012\n", "Running for df1013\n", "Running for df1014\n", "Running for df1015\n", "Running for df1016\n", "Running for df1017\n", "Running for df1018\n", "Running for df1019\n", "Running for df1020\n", "Running for df1021\n", "Running for df1022\n", "Running for df1023\n", "Running for df1024\n", "Running for df1025\n", "Running for df1026\n", "Running for df1027\n", "Running for df1028\n", "Running for df1029\n", "Running for df1030\n", "Running for df1031\n", "Running for df1032\n", "Running for df1033\n", "Running for df1034\n", "Running for df1035\n", "Running for df1036\n", "Running for df1037\n", "Running for df1038\n", "Running for df1039\n", "Running for df1040\n", "Running for df1041\n", "Running for df1042\n", "Running for df1043\n", "Running for df1044\n", "Running for df1045\n", "Running for df1046\n", "Running for df1047\n", "Running for df1048\n", "Running for df1049\n", "Running for df1050\n", "Running for df1051\n", "Running for df1052\n", "Running for df1053\n", "Running for df1054\n", "Running for df1055\n", "Running for df1056\n", "Running for df1057\n", "Running for df1058\n", "Running for df1059\n", "Running for df1060\n", "Running for df1061\n", "Running for df1062\n", "Running for df1063\n", "Running for df1064\n", "Running for df1065\n", "Running for df1066\n", "Running for df1067\n", "Running for df1068\n", "Running for df1069\n", "Running for df1070\n", "Running for df1071\n", "Running for df1072\n", "Running for df1073\n", "Running for df1074\n", "Running for df1075\n", "Running for df1076\n", "Running for df1077\n", "Running for df1078\n", "Running for df1079\n", "Running for df1080\n", "Running for df1081\n", "Running for df1082\n", "Running for df1083\n", "Running for df1084\n", "Running for df1085\n", "Running for df1086\n", "Running for df1087\n", "Running for df1088\n", "Running for df1089\n", "Running for df1090\n", "Running for df1091\n", "Running for df1092\n", "Running for df1093\n", "Running for df1094\n", "Running for df1095\n", "Running for df1096\n", "Running for df1097\n", "Running for df1098\n", "Running for df1099\n", "Running for df1100\n", "Running for df1101\n", "Running for df1102\n", "Running for df1103\n", "Running for df1104\n", "Running for df1105\n", "Running for df1106\n", "Running for df1107\n", "Running for df1108\n", "Running for df1109\n", "Running for df1110\n", "Running for df1111\n", "Running for df1112\n", "Running for df1113\n", "Running for df1114\n", "Running for df1115\n", "Running for df1116\n", "Running for df1117\n", "Running for df1118\n", "Running for df1119\n", "Running for df1120\n", "Running for df1121\n", "Running for df1122\n", "Running for df1123\n", "Running for df1124\n", "Running for df1125\n", "Running for df1126\n", "Running for df1127\n", "Running for df1128\n", "Running for df1129\n", "Running for df1130\n", "Running for df1131\n", "Running for df1132\n", "Running for df1133\n", "Running for df1134\n", "Running for df1135\n", "Running for df1136\n", "Running for df1137\n", "Running for df1138\n", "Running for df1139\n", "Running for df1140\n", "Running for df1141\n", "Running for df1142\n", "Running for df1143\n", "Running for df1144\n", "Running for df1145\n", "Running for df1146\n", "Running for df1147\n", "Running for df1148\n", "Running for df1149\n", "Running for df1150\n", "Running for df1151\n", "Running for df1152\n", "Running for df1153\n", "Running for df1154\n", "Running for df1155\n", "Running for df1156\n", "Running for df1157\n", "Running for df1158\n", "Running for df1159\n", "Running for df1160\n", "Running for df1161\n", "Running for df1162\n", "Running for df1163\n", "Running for df1164\n", "Running for df1165\n", "Running for df1166\n", "Running for df1167\n", "Running for df1168\n", "Running for df1169\n", "Running for df1170\n", "Running for df1171\n", "Running for df1172\n", "Running for df1173\n", "Running for df1174\n", "Running for df1175\n", "Running for df1176\n", "Running for df1177\n", "Running for df1178\n", "Running for df1179\n", "Running for df1180\n", "Running for df1181\n", "Running for df1182\n", "Running for df1183\n", "Running for df1184\n", "Running for df1185\n", "Running for df1186\n", "Running for df1187\n", "Running for df1188\n", "Running for df1189\n", "Running for df1190\n", "Running for df1191\n", "Running for df1192\n", "Running for df1193\n", "Running for df1194\n", "Running for df1195\n", "Running for df1196\n", "Running for df1197\n", "Running for df1198\n", "Running for df1199\n", "Running for df1200\n", "Running for df1201\n", "Running for df1202\n", "Running for df1203\n", "Running for df1204\n", "Running for df1205\n", "Running for df1206\n", "Running for df1207\n", "Running for df1208\n", "Running for df1209\n", "Running for df1210\n", "Running for df1211\n", "Running for df1212\n", "Running for df1213\n", "Running for df1214\n", "Running for df1215\n", "Running for df1216\n", "Running for df1217\n", "Running for df1218\n", "Running for df1219\n", "Running for df1220\n", "Running for df1221\n", "Running for df1222\n", "Running for df1223\n", "Running for df1224\n", "Running for df1225\n", "Running for df1226\n", "Running for df1227\n", "Running for df1228\n", "Running for df1229\n", "Running for df1230\n", "Running for df1231\n", "Running for df1232\n", "Running for df1233\n", "Running for df1234\n", "Running for df1235\n", "Running for df1236\n", "Running for df1237\n", "Running for df1238\n", "Running for df1239\n", "Running for df1240\n", "Running for df1241\n", "Running for df1242\n", "Running for df1243\n", "Running for df1244\n", "Running for df1245\n", "Running for df1246\n", "Running for df1247\n", "Running for df1248\n", "Running for df1249\n", "Running for df1250\n", "Running for df1251\n", "Running for df1252\n", "Running for df1253\n", "Running for df1254\n", "Running for df1255\n", "Running for df1256\n", "Running for df1257\n", "Running for df1258\n", "Running for df1259\n", "Running for df1260\n", "Running for df1261\n", "Running for df1262\n", "Running for df1263\n", "Running for df1264\n", "Running for df1265\n", "Running for df1266\n", "Running for df1267\n", "Running for df1268\n", "Running for df1269\n", "Running for df1270\n", "Running for df1271\n", "Running for df1272\n", "Running for df1273\n", "Running for df1274\n", "Running for df1275\n", "Running for df1276\n", "Running for df1277\n", "Running for df1278\n", "Running for df1279\n", "Running for df1280\n", "Running for df1281\n", "Running for df1282\n", "Running for df1283\n", "Running for df1284\n", "Running for df1285\n", "Running for df1286\n", "Running for df1287\n", "Running for df1288\n", "Running for df1289\n", "Running for df1290\n", "Running for df1291\n", "Running for df1292\n", "Running for df1293\n", "Running for df1294\n", "Running for df1295\n", "Running for df1296\n", "Running for df1297\n", "Running for df1298\n", "Running for df1299\n", "Running for df1300\n", "Running for df1301\n", "Running for df1302\n", "Running for df1303\n", "Running for df1304\n", "Running for df1305\n", "Running for df1306\n", "Running for df1307\n", "Running for df1308\n", "Running for df1309\n", "Running for df1310\n", "Running for df1311\n", "Running for df1312\n", "Running for df1313\n", "Running for df1314\n", "Running for df1315\n", "Running for df1316\n", "Running for df1317\n", "Running for df1318\n", "Running for df1319\n", "Running for df1320\n", "Running for df1321\n", "Running for df1322\n", "Running for df1323\n", "Running for df1324\n", "Running for df1325\n", "Running for df1326\n", "Running for df1327\n", "Running for df1328\n", "Running for df1329\n", "Running for df1330\n", "Running for df1331\n", "Running for df1332\n", "Running for df1333\n", "Running for df1334\n", "Running for df1335\n", "Running for df1336\n", "Running for df1337\n", "Running for df1338\n", "Running for df1339\n", "Running for df1340\n", "Running for df1341\n", "Running for df1342\n", "Running for df1343\n", "Running for df1344\n", "Running for df1345\n", "Running for df1346\n", "Running for df1347\n", "Running for df1348\n", "Running for df1349\n", "Running for df1350\n", "Running for df1351\n", "Running for df1352\n", "Running for df1353\n", "Running for df1354\n", "Running for df1355\n", "Running for df1356\n", "Running for df1357\n", "Running for df1358\n", "Running for df1359\n", "Running for df1360\n", "Running for df1361\n", "Running for df1362\n", "Running for df1363\n", "Running for df1364\n", "Running for df1365\n", "Running for df1366\n", "Running for df1367\n", "Running for df1368\n", "Running for df1369\n", "Running for df1370\n", "Running for df1371\n", "Running for df1372\n", "Running for df1373\n", "Running for df1374\n", "Running for df1375\n", "Running for df1376\n", "Running for df1377\n", "Running for df1378\n", "Running for df1379\n", "Running for df1380\n", "Running for df1381\n", "Running for df1382\n", "Running for df1383\n", "Running for df1384\n", "Running for df1385\n", "Running for df1386\n", "Running for df1387\n", "Running for df1388\n", "Running for df1389\n", "Running for df1390\n", "Running for df1391\n", "Running for df1392\n", "Running for df1393\n", "Running for df1394\n", "Running for df1395\n", "Running for df1396\n", "Running for df1397\n", "Running for df1398\n", "Running for df1399\n", "Running for df1400\n", "Running for df1401\n", "Running for df1402\n", "Running for df1403\n", "Running for df1404\n", "Running for df1405\n", "Running for df1406\n", "Running for df1407\n", "Running for df1408\n", "Running for df1409\n", "Running for df1410\n", "Running for df1411\n", "Running for df1412\n", "Running for df1413\n", "Running for df1414\n", "Running for df1415\n", "Running for df1416\n", "Running for df1417\n", "Running for df1418\n", "Running for df1419\n", "Running for df1420\n", "Running for df1421\n", "Running for df1422\n", "Running for df1423\n", "Running for df1424\n", "Running for df1425\n", "Running for df1426\n", "Running for df1427\n", "Running for df1428\n", "Running for df1429\n", "Running for df1430\n", "Running for df1431\n", "Running for df1432\n", "Running for df1433\n", "Running for df1434\n", "Running for df1435\n", "Running for df1436\n", "Running for df1437\n", "Running for df1438\n", "Running for df1439\n", "Running for df1440\n", "Running for df1441\n", "Running for df1442\n", "Running for df1443\n", "Running for df1444\n", "Running for df1445\n", "Running for df1446\n", "Running for df1447\n", "Running for df1448\n", "Running for df1449\n", "Running for df1450\n", "Running for df1451\n", "Running for df1452\n", "Running for df1453\n", "Running for df1454\n", "Running for df1455\n", "Running for df1456\n", "Running for df1457\n", "Running for df1458\n", "Running for df1459\n", "Running for df1460\n", "Running for df1461\n", "Running for df1462\n", "Running for df1463\n", "Running for df1464\n", "Running for df1465\n", "Running for df1466\n", "Running for df1467\n", "Running for df1468\n", "Running for df1469\n", "Running for df1470\n", "Running for df1471\n", "Running for df1472\n", "Running for df1473\n", "Running for df1474\n", "Running for df1475\n", "Running for df1476\n", "Running for df1477\n", "Running for df1478\n", "Running for df1479\n", "Running for df1480\n", "Running for df1481\n", "Running for df1482\n", "Running for df1483\n", "Running for df1484\n", "Running for df1485\n", "Running for df1486\n", "Running for df1487\n", "Running for df1488\n", "Running for df1489\n", "Running for df1490\n", "Running for df1491\n", "Running for df1492\n", "Running for df1493\n", "Running for df1494\n", "Running for df1495\n", "Running for df1496\n", "Running for df1497\n", "Running for df1498\n", "Running for df1499\n", "Running for df1500\n", "Running for df1501\n", "Running for df1502\n", "Running for df1503\n", "Running for df1504\n", "Running for df1505\n", "Running for df1506\n", "Running for df1507\n", "Running for df1508\n", "Running for df1509\n", "Running for df1510\n", "Running for df1511\n", "Running for df1512\n", "Running for df1513\n", "Running for df1514\n", "Running for df1515\n", "Running for df1516\n", "Running for df1517\n", "Running for df1518\n", "Running for df1519\n", "Running for df1520\n", "Running for df1521\n", "Running for df1522\n", "Running for df1523\n", "Running for df1524\n", "Running for df1525\n", "Running for df1526\n", "Running for df1527\n", "Running for df1528\n", "Running for df1529\n", "Running for df1530\n", "Running for df1531\n", "Running for df1532\n", "Running for df1533\n", "Running for df1534\n", "Running for df1535\n", "Running for df1536\n", "Running for df1537\n", "Running for df1538\n", "Running for df1539\n", "Running for df1540\n", "Running for df1541\n", "Running for df1542\n", "Running for df1543\n", "Running for df1544\n", "Running for df1545\n", "Running for df1546\n", "Running for df1547\n", "Running for df1548\n", "Running for df1549\n", "Running for df1550\n", "Running for df1551\n", "Running for df1552\n", "Running for df1553\n", "Running for df1554\n", "Running for df1555\n", "Running for df1556\n", "Running for df1557\n", "Running for df1558\n", "Running for df1559\n", "Running for df1560\n", "Running for df1561\n", "Running for df1562\n", "Running for df1563\n", "Running for df1564\n", "Running for df1565\n", "Running for df1566\n", "Running for df1567\n", "Running for df1568\n", "Running for df1569\n", "Running for df1570\n", "Running for df1571\n", "Running for df1572\n", "Running for df1573\n", "Running for df1574\n", "Running for df1575\n", "Running for df1576\n", "Running for df1577\n", "Running for df1578\n", "Running for df1579\n", "Running for df1580\n", "Running for df1581\n", "Running for df1582\n", "Running for df1583\n", "Running for df1584\n", "Running for df1585\n", "Running for df1586\n", "Running for df1587\n", "Running for df1588\n", "Running for df1589\n", "Running for df1590\n", "Running for df1591\n", "Running for df1592\n", "Running for df1593\n", "Running for df1594\n", "Running for df1595\n", "Running for df1596\n", "Running for df1597\n", "Running for df1598\n", "Running for df1599\n", "Running for df1600\n", "Running for df1601\n", "Running for df1602\n", "Running for df1603\n", "Running for df1604\n", "Running for df1605\n", "Running for df1606\n", "Running for df1607\n", "Running for df1608\n", "Running for df1609\n", "Running for df1610\n", "Running for df1611\n", "Running for df1612\n", "Running for df1613\n", "Running for df1614\n", "Running for df1615\n", "Running for df1616\n", "Running for df1617\n", "Running for df1618\n", "Running for df1619\n", "Running for df1620\n", "Running for df1621\n", "Running for df1622\n", "Running for df1623\n", "Running for df1624\n", "Running for df1625\n", "Running for df1626\n", "Running for df1627\n", "Running for df1628\n", "Running for df1629\n", "Running for df1630\n", "Running for df1631\n", "Running for df1632\n", "Running for df1633\n", "Running for df1634\n", "Running for df1635\n", "Running for df1636\n", "Running for df1637\n", "Running for df1638\n", "Running for df1639\n", "Running for df1640\n", "Running for df1641\n", "Running for df1642\n", "Running for df1643\n", "Running for df1644\n", "Running for df1645\n", "Running for df1646\n", "Running for df1647\n", "Running for df1648\n", "Running for df1649\n", "Running for df1650\n", "Running for df1651\n", "Running for df1652\n", "Running for df1653\n", "Running for df1654\n", "Running for df1655\n", "Running for df1656\n", "Running for df1657\n", "Running for df1658\n", "Running for df1659\n", "Running for df1660\n", "Running for df1661\n", "Running for df1662\n", "Running for df1663\n", "Running for df1664\n", "Running for df1665\n", "Running for df1666\n", "Running for df1667\n", "Running for df1668\n", "Running for df1669\n", "Running for df1670\n", "Running for df1671\n", "Running for df1672\n", "Running for df1673\n", "Running for df1674\n", "Running for df1675\n", "Running for df1676\n", "Running for df1677\n", "Running for df1678\n", "Running for df1679\n", "Running for df1680\n", "Running for df1681\n", "Running for df1682\n", "Running for df1683\n", "Running for df1684\n", "Running for df1685\n", "Running for df1686\n", "Running for df1687\n", "Running for df1688\n", "Running for df1689\n", "Running for df1690\n", "Running for df1691\n", "Running for df1692\n", "Running for df1693\n", "Running for df1694\n", "Running for df1695\n", "Running for df1696\n", "Running for df1697\n", "Running for df1698\n", "Running for df1699\n", "Running for df1700\n", "Running for df1701\n", "Running for df1702\n", "Running for df1703\n", "Running for df1704\n", "Running for df1705\n", "Running for df1706\n", "Running for df1707\n", "Running for df1708\n", "Running for df1709\n", "Running for df1710\n", "Running for df1711\n", "Running for df1712\n", "Running for df1713\n", "Running for df1714\n", "Running for df1715\n", "Running for df1716\n", "Running for df1717\n", "Running for df1718\n", "Running for df1719\n", "Running for df1720\n", "Running for df1721\n", "Running for df1722\n", "Running for df1723\n", "Running for df1724\n", "Running for df1725\n", "Running for df1726\n", "Running for df1727\n", "Running for df1728\n", "Running for df1729\n", "Running for df1730\n", "Running for df1731\n", "Running for df1732\n", "Running for df1733\n", "Running for df1734\n", "Running for df1735\n", "Running for df1736\n", "Running for df1737\n", "Running for df1738\n", "Running for df1739\n", "Running for df1740\n", "Running for df1741\n", "Running for df1742\n", "Running for df1743\n", "Running for df1744\n", "Running for df1745\n", "Running for df1746\n", "Running for df1747\n", "Running for df1748\n", "Running for df1749\n", "Running for df1750\n", "Running for df1751\n", "Running for df1752\n", "Running for df1753\n", "Running for df1754\n", "Running for df1755\n", "Running for df1756\n", "Running for df1757\n", "Running for df1758\n", "Running for df1759\n", "Running for df1760\n", "Running for df1761\n", "Running for df1762\n", "Running for df1763\n", "Running for df1764\n", "Running for df1765\n", "Running for df1766\n", "Running for df1767\n", "Running for df1768\n", "Running for df1769\n", "Running for df1770\n", "Running for df1771\n", "Running for df1772\n", "Running for df1773\n", "Running for df1774\n", "Running for df1775\n", "Running for df1776\n", "Running for df1777\n", "Running for df1778\n", "Running for df1779\n", "Running for df1780\n", "Running for df1781\n", "Running for df1782\n", "Running for df1783\n", "Running for df1784\n", "Running for df1785\n", "Running for df1786\n", "Running for df1787\n", "Running for df1788\n", "Running for df1789\n", "Running for df1790\n", "Running for df1791\n", "Running for df1792\n", "Running for df1793\n", "Running for df1794\n", "Running for df1795\n", "Running for df1796\n", "Running for df1797\n", "Running for df1798\n", "Running for df1799\n", "Running for df1800\n", "Running for df1801\n", "Running for df1802\n", "Running for df1803\n", "Running for df1804\n", "Running for df1805\n", "Running for df1806\n", "Running for df1807\n", "Running for df1808\n", "Running for df1809\n", "Running for df1810\n", "Running for df1811\n", "Running for df1812\n", "Running for df1813\n", "Running for df1814\n", "Running for df1815\n", "Running for df1816\n", "Running for df1817\n", "Running for df1818\n", "Running for df1819\n", "Running for df1820\n", "Running for df1821\n", "Running for df1822\n", "Running for df1823\n", "Running for df1824\n", "Running for df1825\n", "Running for df1826\n", "Running for df1827\n", "Running for df1828\n", "Running for df1829\n", "Running for df1830\n", "Running for df1831\n", "Running for df1832\n", "Running for df1833\n", "Running for df1834\n", "Running for df1835\n", "Running for df1836\n", "Running for df1837\n", "Running for df1838\n", "Running for df1839\n", "Running for df1840\n", "Running for df1841\n", "Running for df1842\n", "Running for df1843\n", "Running for df1844\n", "Running for df1845\n", "Running for df1846\n", "Running for df1847\n", "Running for df1848\n", "Running for df1849\n", "Running for df1850\n", "Running for df1851\n", "Running for df1852\n", "Running for df1853\n", "Running for df1854\n", "Running for df1855\n", "Running for df1856\n", "Running for df1857\n", "Running for df1858\n", "Running for df1859\n", "Running for df1860\n", "Running for df1861\n", "Running for df1862\n", "Running for df1863\n", "Running for df1864\n", "Running for df1865\n", "Running for df1866\n", "Running for df1867\n", "Running for df1868\n", "Running for df1869\n", "Running for df1870\n", "Running for df1871\n", "Running for df1872\n", "Running for df1873\n", "Running for df1874\n", "Running for df1875\n", "Running for df1876\n", "Running for df1877\n", "Running for df1878\n", "Running for df1879\n", "Running for df1880\n", "Running for df1881\n", "Running for df1882\n", "Running for df1883\n", "Running for df1884\n", "Running for df1885\n", "Running for df1886\n", "Running for df1887\n", "Running for df1888\n", "Running for df1889\n", "Running for df1890\n", "Running for df1891\n", "Running for df1892\n", "Running for df1893\n", "Running for df1894\n", "Running for df1895\n", "Running for df1896\n", "Running for df1897\n", "Running for df1898\n", "Running for df1899\n", "Running for df1900\n", "Running for df1901\n", "Running for df1902\n", "Running for df1903\n", "Running for df1904\n", "Running for df1905\n", "Running for df1906\n", "Running for df1907\n", "Running for df1908\n", "Running for df1909\n", "Running for df1910\n", "Running for df1911\n", "Running for df1912\n", "Running for df1913\n", "Running for df1914\n", "Running for df1915\n", "Running for df1916\n", "Running for df1917\n", "Running for df1918\n", "Running for df1919\n", "Running for df1920\n", "Running for df1921\n", "Running for df1922\n", "Running for df1923\n", "Running for df1924\n", "Running for df1925\n", "Running for df1926\n", "Running for df1927\n", "Running for df1928\n", "Running for df1929\n", "Running for df1930\n", "Running for df1931\n", "Running for df1932\n", "Running for df1933\n", "Running for df1934\n", "Running for df1935\n", "Running for df1936\n", "Running for df1937\n", "Running for df1938\n", "Running for df1939\n", "Running for df1940\n", "Running for df1941\n", "Running for df1942\n", "Running for df1943\n", "Running for df1944\n", "Running for df1945\n", "Running for df1946\n", "Running for df1947\n", "Running for df1948\n", "Running for df1949\n", "Running for df1950\n", "Running for df1951\n", "Running for df1952\n", "Running for df1953\n", "Running for df1954\n", "Running for df1955\n", "Running for df1956\n", "Running for df1957\n", "Running for df1958\n", "Running for df1959\n", "Running for df1960\n", "Running for df1961\n", "Running for df1962\n", "Running for df1963\n", "Running for df1964\n", "Running for df1965\n", "Running for df1966\n", "Running for df1967\n", "Running for df1968\n", "Running for df1969\n", "Running for df1970\n", "Running for df1971\n", "Running for df1972\n", "Running for df1973\n", "Running for df1974\n", "Running for df1975\n", "Running for df1976\n", "Running for df1977\n", "Running for df1978\n", "Running for df1979\n", "Running for df1980\n", "Running for df1981\n", "Running for df1982\n", "Running for df1983\n", "Running for df1984\n", "Running for df1985\n", "Running for df1986\n", "Running for df1987\n", "Running for df1988\n", "Running for df1989\n", "Running for df1990\n", "Running for df1991\n", "Running for df1992\n", "Running for df1993\n", "Running for df1994\n", "Running for df1995\n", "Running for df1996\n", "Running for df1997\n", "Running for df1998\n", "Running for df1999\n", "Running for df2000\n", "Running for df2001\n", "Running for df2002\n", "Running for df2003\n", "Running for df2004\n", "Running for df2005\n", "Running for df2006\n", "Running for df2007\n", "Running for df2008\n", "Running for df2009\n", "Running for df2010\n", "Running for df2011\n", "Running for df2012\n", "Running for df2013\n", "Running for df2014\n", "Running for df2015\n", "Running for df2016\n", "Running for df2017\n", "Running for df2018\n", "Running for df2019\n", "Running for df2020\n", "Running for df2021\n", "Running for df2022\n", "Running for df2023\n", "Running for df2024\n", "Running for df2025\n", "Running for df2026\n", "Running for df2027\n", "Running for df2028\n", "Running for df2029\n", "Running for df2030\n", "Running for df2031\n", "Running for df2032\n", "Running for df2033\n", "Running for df2034\n", "Running for df2035\n", "Running for df2036\n", "Running for df2037\n", "Running for df2038\n", "Running for df2039\n", "Running for df2040\n", "Running for df2041\n", "Running for df2042\n", "Running for df2043\n", "Running for df2044\n", "Running for df2045\n", "Running for df2046\n", "Running for df2047\n", "Running for df2048\n", "Running for df2049\n", "Running for df2050\n", "Running for df2051\n", "Running for df2052\n", "Running for df2053\n", "Running for df2054\n", "Running for df2055\n", "Running for df2056\n", "Running for df2057\n", "Running for df2058\n", "Running for df2059\n", "Running for df2060\n", "Running for df2061\n", "Running for df2062\n", "Running for df2063\n", "Running for df2064\n", "Running for df2065\n", "Running for df2066\n", "Running for df2067\n", "Running for df2068\n", "Running for df2069\n", "Running for df2070\n", "Running for df2071\n", "Running for df2072\n", "Running for df2073\n", "Running for df2074\n", "Running for df2075\n", "Running for df2076\n", "Running for df2077\n", "Running for df2078\n", "Running for df2079\n", "Running for df2080\n", "Running for df2081\n", "Running for df2082\n", "Running for df2083\n", "Running for df2084\n", "Running for df2085\n", "Running for df2086\n", "Running for df2087\n", "Running for df2088\n", "Running for df2089\n", "Running for df2090\n", "Running for df2091\n", "Running for df2092\n", "Running for df2093\n", "Running for df2094\n", "Running for df2095\n", "Running for df2096\n", "Running for df2097\n", "Running for df2098\n", "Running for df2099\n", "Running for df2100\n", "Running for df2101\n", "Running for df2102\n", "Running for df2103\n", "Running for df2104\n", "Running for df2105\n", "Running for df2106\n", "Running for df2107\n", "Running for df2108\n", "Running for df2109\n", "Running for df2110\n", "Running for df2111\n", "Running for df2112\n", "Running for df2113\n", "Running for df2114\n", "Running for df2115\n", "Running for df2116\n", "Running for df2117\n", "Running for df2118\n", "Running for df2119\n", "Running for df2120\n", "Running for df2121\n", "Running for df2122\n", "Running for df2123\n", "Running for df2124\n", "Running for df2125\n", "Running for df2126\n", "Running for df2127\n", "Running for df2128\n", "Running for df2129\n", "Running for df2130\n", "Running for df2131\n", "Running for df2132\n", "Running for df2133\n", "Running for df2134\n", "Running for df2135\n", "Running for df2136\n", "Running for df2137\n", "Running for df2138\n", "Running for df2139\n", "Running for df2140\n", "Running for df2141\n", "Running for df2142\n", "Running for df2143\n", "Running for df2144\n", "Running for df2145\n", "Running for df2146\n", "Running for df2147\n", "Running for df2148\n", "Running for df2149\n", "Running for df2150\n", "Running for df2151\n", "Running for df2152\n", "Running for df2153\n", "Running for df2154\n", "Running for df2155\n", "Running for df2156\n", "Running for df2157\n", "Running for df2158\n", "Running for df2159\n", "Running for df2160\n", "Running for df2161\n", "Running for df2162\n", "Running for df2163\n", "Running for df2164\n", "Running for df2165\n", "Running for df2166\n", "Running for df2167\n", "Running for df2168\n", "Running for df2169\n", "Running for df2170\n", "Running for df2171\n", "Running for df2172\n", "Running for df2173\n", "Running for df2174\n", "Running for df2175\n", "Running for df2176\n", "Running for df2177\n", "Running for df2178\n", "Running for df2179\n", "Running for df2180\n", "Running for df2181\n", "Running for df2182\n", "Running for df2183\n", "Running for df2184\n", "Running for df2185\n", "Running for df2186\n", "Running for df2187\n", "Running for df2188\n", "Running for df2189\n", "Running for df2190\n", "Running for df2191\n", "Running for df2192\n", "Running for df2193\n", "Running for df2194\n", "Running for df2195\n", "Running for df2196\n", "Running for df2197\n", "Running for df2198\n", "Running for df2199\n", "Running for df2200\n", "Running for df2201\n", "Running for df2202\n", "Running for df2203\n", "Running for df2204\n", "Running for df2205\n", "Running for df2206\n", "Running for df2207\n", "Running for df2208\n", "Running for df2209\n", "Running for df2210\n", "Running for df2211\n", "Running for df2212\n", "Running for df2213\n", "Running for df2214\n", "Running for df2215\n", "Running for df2216\n", "Running for df2217\n", "Running for df2218\n", "Running for df2219\n", "Running for df2220\n", "Running for df2221\n", "Running for df2222\n", "Running for df2223\n", "Running for df2224\n", "Running for df2225\n", "Running for df2226\n", "Running for df2227\n", "Running for df2228\n", "Running for df2229\n", "Running for df2230\n", "Running for df2231\n", "Running for df2232\n", "Running for df2233\n", "Running for df2234\n", "Running for df2235\n", "Running for df2236\n", "Running for df2237\n", "Running for df2238\n", "Running for df2239\n", "Running for df2240\n", "Running for df2241\n", "Running for df2242\n", "Running for df2243\n", "Running for df2244\n", "Running for df2245\n", "Running for df2246\n", "Running for df2247\n", "Running for df2248\n", "Running for df2249\n", "Running for df2250\n", "Running for df2251\n", "Running for df2252\n", "Running for df2253\n", "Running for df2254\n", "Running for df2255\n", "Running for df2256\n", "Running for df2257\n", "Running for df2258\n", "Running for df2259\n", "Running for df2260\n", "Running for df2261\n", "Running for df2262\n", "Running for df2263\n", "Running for df2264\n", "Running for df2265\n", "Running for df2266\n", "Running for df2267\n", "Running for df2268\n", "Running for df2269\n", "Running for df2270\n", "Running for df2271\n", "Running for df2272\n", "Running for df2273\n", "Running for df2274\n", "Running for df2275\n", "Running for df2276\n", "Running for df2277\n", "Running for df2278\n", "Running for df2279\n", "Running for df2280\n", "Running for df2281\n", "Running for df2282\n", "Running for df2283\n", "Running for df2284\n", "Running for df2285\n", "Running for df2286\n", "Running for df2287\n", "Running for df2288\n", "Running for df2289\n", "Running for df2290\n", "Running for df2291\n", "Running for df2292\n", "Running for df2293\n", "Running for df2294\n", "Running for df2295\n", "Running for df2296\n", "Running for df2297\n", "Running for df2298\n", "Running for df2299\n", "Running for df2300\n", "Running for df2301\n", "Running for df2302\n", "Running for df2303\n", "Running for df2304\n", "Running for df2305\n", "Running for df2306\n", "Running for df2307\n", "Running for df2308\n", "Running for df2309\n", "Running for df2310\n", "Running for df2311\n", "Running for df2312\n", "Running for df2313\n", "Running for df2314\n", "Running for df2315\n", "Running for df2316\n", "Running for df2317\n", "Running for df2318\n", "Running for df2319\n", "Running for df2320\n", "Running for df2321\n", "Running for df2322\n", "Running for df2323\n", "Running for df2324\n", "Running for df2325\n", "Running for df2326\n", "Running for df2327\n", "Running for df2328\n", "Running for df2329\n", "Running for df2330\n", "Running for df2331\n", "Running for df2332\n", "Running for df2333\n", "Running for df2334\n", "Running for df2335\n", "Running for df2336\n", "Running for df2337\n", "Running for df2338\n", "Running for df2339\n", "Running for df2340\n", "Running for df2341\n", "Running for df2342\n", "Running for df2343\n", "Running for df2344\n", "Running for df2345\n", "Running for df2346\n", "Running for df2347\n", "Running for df2348\n", "Running for df2349\n", "Running for df2350\n", "Running for df2351\n", "Running for df2352\n", "Running for df2353\n", "Running for df2354\n", "Running for df2355\n", "Running for df2356\n", "Running for df2357\n", "Running for df2358\n", "Running for df2359\n", "Running for df2360\n", "Running for df2361\n", "Running for df2362\n", "Running for df2363\n", "Running for df2364\n", "Running for df2365\n", "Running for df2366\n", "Running for df2367\n", "Running for df2368\n", "Running for df2369\n", "Running for df2370\n", "Running for df2371\n", "Running for df2372\n", "Running for df2373\n", "Running for df2374\n", "Running for df2375\n", "Running for df2376\n", "Running for df2377\n", "Running for df2378\n", "Running for df2379\n", "Running for df2380\n", "Running for df2381\n", "Running for df2382\n", "Running for df2383\n", "Running for df2384\n", "Running for df2385\n", "Running for df2386\n", "Running for df2387\n", "Running for df2388\n", "Running for df2389\n", "Running for df2390\n", "Running for df2391\n", "Running for df2392\n", "Running for df2393\n", "Running for df2394\n", "Running for df2395\n", "Running for df2396\n", "Running for df2397\n", "Running for df2398\n", "Running for df2399\n", "Running for df2400\n", "Running for df2401\n", "Running for df2402\n", "Running for df2403\n", "Running for df2404\n", "Running for df2405\n", "Running for df2406\n", "Running for df2407\n", "Running for df2408\n", "Running for df2409\n", "Running for df2410\n", "Running for df2411\n", "Running for df2412\n", "Running for df2413\n", "Running for df2414\n", "Running for df2415\n", "Running for df2416\n", "Running for df2417\n", "Running for df2418\n", "Running for df2419\n", "Running for df2420\n", "Running for df2421\n", "Running for df2422\n", "Running for df2423\n", "Running for df2424\n", "Running for df2425\n", "Running for df2426\n", "Running for df2427\n", "Running for df2428\n", "Running for df2429\n", "Running for df2430\n", "Running for df2431\n", "Running for df2432\n", "Running for df2433\n", "Running for df2434\n", "Running for df2435\n", "Running for df2436\n", "Running for df2437\n", "Running for df2438\n", "Running for df2439\n", "Running for df2440\n"]}], "source": ["combined_list = []\n", "\n", "for i in range(2, len(df_names)):\n", "\n", "    print(f\"Running for df{i}\")\n", "\n", "    ## Part 1\n", "    df_new = pd.DataFrame(globals()[f\"df{i}\"].head(1))\n", "    \n", "    part1_df = df_new['Col'].apply(extract_data_and_append) # Apply the function to each row in the DataFrame   \n", "    part1_df = pd.DataFrame(part1_df.tolist()) # Convert the processed data back into a DataFrame\n", "    #print(part1_df.head())\n", "\n", "    ## Part 2\n", "    df_xyz = pd.DataFrame(globals()[f\"df{i}\"].iloc[1:])\n", "\n", "    part2_df = df_xyz['Col'].apply(process_row)\n", "    part2_df = pd.DataFrame(part2_df.tolist())\n", "    #part2_df.head()\n", "\n", "    ## Comb<PERSON>\n", "    df_combined = pd.concat([part1_df, part2_df], axis=0, ignore_index=True)\n", "    df_combined.loc[len(df_combined)] = [''] * len(df_combined.columns)\n", "    \n", "    # Append the current df_combined to the list\n", "    combined_list.append(df_combined)\n", "\n", "# After the loop, concatenate all the dataframes in combined_list into one final mega dataframe\n", "final_mega_df = pd.concat(combined_list, axis=0, ignore_index=True)"]}, {"cell_type": "code", "execution_count": 13, "id": "14bd9fdd-32f3-48ac-88cb-955c53650cb6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Column 1</th>\n", "      <th>Column 2</th>\n", "      <th>Column 3</th>\n", "      <th>Column 4</th>\n", "      <th>Column 5</th>\n", "      <th>Column 6</th>\n", "      <th>Column 7</th>\n", "      <th>Column 8</th>\n", "      <th>Column 9</th>\n", "      <th>Column 10</th>\n", "      <th>Column 11</th>\n", "      <th>Column 12</th>\n", "      <th>Column 13</th>\n", "      <th>Column 14</th>\n", "      <th>Column 15</th>\n", "      <th>Column 16</th>\n", "      <th>Column 17</th>\n", "      <th>Column 18</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>7500229</td>\n", "      <td></td>\n", "      <td>GLASS LEFF 25CL</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>DE30</td>\n", "      <td>BREMEN POCM-DC SOL</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td></td>\n", "      <td>101</td>\n", "      <td>1050</td>\n", "      <td>5005950087</td>\n", "      <td>3.240</td>\n", "      <td>PC</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>05.04.2019</td>\n", "      <td></td>\n", "      <td>05.04.2019</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.936,35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td></td>\n", "      <td>641</td>\n", "      <td>1050</td>\n", "      <td>4914519543</td>\n", "      <td>3.240-</td>\n", "      <td>PC</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>05.04.2019</td>\n", "      <td></td>\n", "      <td>05.04.2019</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.936,35-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td></td>\n", "      <td>101</td>\n", "      <td>1050</td>\n", "      <td>5005943439</td>\n", "      <td>3.240</td>\n", "      <td>PC</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>04.04.2019</td>\n", "      <td></td>\n", "      <td>04.04.2019</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.948,40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td></td>\n", "      <td>102</td>\n", "      <td>1050</td>\n", "      <td>5005950085</td>\n", "      <td>3.240-</td>\n", "      <td>PC</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>04.04.2019</td>\n", "      <td></td>\n", "      <td>05.04.2019</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.948,40-</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Column 1 Column 2 Column 3         Column 4 Column 5 Column 6 Column 7  \\\n", "0           7500229           GLASS LEFF 25CL                              \n", "1               101     1050       5005950087    3.240       PC            \n", "2               641     1050       4914519543   3.240-       PC            \n", "3               101     1050       5005943439    3.240       PC            \n", "4               102     1050       5005950085   3.240-       PC            \n", "\n", "  Column 8 Column 9           Column 10   Column 11 Column 12   Column 13  \\\n", "0              DE30  BREMEN POCM-DC SOL                                     \n", "1                                        05.04.2019            05.04.2019   \n", "2                                        05.04.2019            05.04.2019   \n", "3                                        04.04.2019            04.04.2019   \n", "4                                        04.04.2019            05.04.2019   \n", "\n", "  Column 14 Column 15 Column 16 Column 17  Column 18  \n", "0                                                     \n", "1                                           2.936,35  \n", "2                                          2.936,35-  \n", "3                                           2.948,40  \n", "4                                          2.948,40-  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["final_mega_df.head()"]}, {"cell_type": "code", "execution_count": 14, "id": "e6032364-7a36-48fb-9fd2-24b9eee57975", "metadata": {}, "outputs": [{"data": {"text/plain": ["(695008, 18)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["final_mega_df.shape "]}, {"cell_type": "code", "execution_count": 15, "id": "e6a26516-d039-4ba5-a82e-a095a1340090", "metadata": {}, "outputs": [], "source": ["def fix_minus(val):\n", "    if val.endswith('-'):\n", "        return f\"-{val[:-1]}\"  # Move minus to the front and remove the last character\n", "    return val"]}, {"cell_type": "code", "execution_count": 16, "id": "37076343-2bfc-4411-abda-51f1f9072883", "metadata": {}, "outputs": [], "source": ["final_mega_df['Column 5'] = final_mega_df['Column 5'].apply(fix_minus)"]}, {"cell_type": "code", "execution_count": 17, "id": "06743b9b-2321-4f83-b088-bb6a4ae76331", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Column 1</th>\n", "      <th>Column 2</th>\n", "      <th>Column 3</th>\n", "      <th>Column 4</th>\n", "      <th>Column 5</th>\n", "      <th>Column 6</th>\n", "      <th>Column 7</th>\n", "      <th>Column 8</th>\n", "      <th>Column 9</th>\n", "      <th>Column 10</th>\n", "      <th>Column 11</th>\n", "      <th>Column 12</th>\n", "      <th>Column 13</th>\n", "      <th>Column 14</th>\n", "      <th>Column 15</th>\n", "      <th>Column 16</th>\n", "      <th>Column 17</th>\n", "      <th>Column 18</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>7500229</td>\n", "      <td></td>\n", "      <td>GLASS LEFF 25CL</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>DE30</td>\n", "      <td>BREMEN POCM-DC SOL</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td></td>\n", "      <td>101</td>\n", "      <td>1050</td>\n", "      <td>5005950087</td>\n", "      <td>3.240</td>\n", "      <td>PC</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>05.04.2019</td>\n", "      <td></td>\n", "      <td>05.04.2019</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.936,35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td></td>\n", "      <td>641</td>\n", "      <td>1050</td>\n", "      <td>4914519543</td>\n", "      <td>-3.240</td>\n", "      <td>PC</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>05.04.2019</td>\n", "      <td></td>\n", "      <td>05.04.2019</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.936,35-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td></td>\n", "      <td>101</td>\n", "      <td>1050</td>\n", "      <td>5005943439</td>\n", "      <td>3.240</td>\n", "      <td>PC</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>04.04.2019</td>\n", "      <td></td>\n", "      <td>04.04.2019</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.948,40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td></td>\n", "      <td>102</td>\n", "      <td>1050</td>\n", "      <td>5005950085</td>\n", "      <td>-3.240</td>\n", "      <td>PC</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>04.04.2019</td>\n", "      <td></td>\n", "      <td>05.04.2019</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>2.948,40-</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Column 1 Column 2 Column 3         Column 4 Column 5 Column 6 Column 7  \\\n", "0           7500229           GLASS LEFF 25CL                              \n", "1               101     1050       5005950087    3.240       PC            \n", "2               641     1050       4914519543   -3.240       PC            \n", "3               101     1050       5005943439    3.240       PC            \n", "4               102     1050       5005950085   -3.240       PC            \n", "\n", "  Column 8 Column 9           Column 10   Column 11 Column 12   Column 13  \\\n", "0              DE30  BREMEN POCM-DC SOL                                     \n", "1                                        05.04.2019            05.04.2019   \n", "2                                        05.04.2019            05.04.2019   \n", "3                                        04.04.2019            04.04.2019   \n", "4                                        04.04.2019            05.04.2019   \n", "\n", "  Column 14 Column 15 Column 16 Column 17  Column 18  \n", "0                                                     \n", "1                                           2.936,35  \n", "2                                          2.936,35-  \n", "3                                           2.948,40  \n", "4                                          2.948,40-  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["final_mega_df.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "e34c6b9f-4f93-4fe2-a8fd-0dc7a20c0910", "metadata": {}, "outputs": [], "source": ["# final_mega_df['Column 2'] = final_mega_df['Column 2'].astype('int64')\n", "# final_mega_df['Column 3'] = final_mega_df['Column 3'].astype('int64')\n", "# final_mega_df['Column 5'] = final_mega_df['Column 5'].astype('int64')"]}, {"cell_type": "code", "execution_count": 19, "id": "a57c08b5-3851-4db6-b4f1-51f8c45c3597", "metadata": {}, "outputs": [], "source": ["final_mega_df.to_excel('clean_MB51_export.xlsx', index= False)"]}, {"cell_type": "code", "execution_count": null, "id": "b38e96b9-1ed4-4cee-8f66-a63c3703755f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b4607e70", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}